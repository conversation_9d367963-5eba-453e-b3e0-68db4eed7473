@echo on

set "qt_version=5.15.16"
set "qt6_version=6.7.3"
set "openssl_version=3.4.0"
set "ghSsl_user=xanasoft"
set "ghSsl_repo=openssl-builds"
set "ghQtBuilds_user=xanasoft"
set "ghQtBuilds_repo=qt-builds"
set "ghQtBuilds_hash_x86=502e9a36a52918af4e116cd74c16c6c260d029087aaeee3775ab0e5d3f6a2705"
set "ghQtBuilds_hash_x64=673c288feeabd11ec66f9f454d49cde3945cbd3e3f71283b7a6c4df0893b19f2"

REM catch build_qt6
set "allArgs=%*"
set "allArgsCatch=%allArgs:build_qt6=%"
if not "%~1" == "" (
    if not "%allArgs%" == "%allArgsCatch%" (
        set "qt_version=%qt6_version%"
    ) else (
        set "qt_version=%qt_version%"
    )
)
