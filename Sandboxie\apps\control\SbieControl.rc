// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"
#include "verrsrc.h"
#include "common/my_version.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (United States) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)

/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
AAAPPICON               ICON                    "../res/controlwin.ico"

INITWAIT1               ICON                    "../res/initwait1.ico"

INITWAIT2               ICON                    "../res/initwait2.ico"

TRAYICON_FULL           ICON                    "../res/sandbox-full.ico"

TRAYICON_EMPTY          ICON                    "../res/sandbox-empty.ico"

TRAYICON_FULL_DFP       ICON                    "../res/sandbox-full-dfp.ico"

TRAYICON_EMPTY_DFP      ICON                    "../res/sandbox-empty-dfp.ico"

TRAYICON_DELETE         ICON                    "../res/sandbox-delete.ico"

PROCESS_ICON_EMPTY      ICON                    "../res/proc-empty.ico"

PROCESS_ICON_MINUS      ICON                    "../res/proc-full-minus.ico"

PROCESS_ICON_PLUS       ICON                    "../res/proc-full-plus.ico"

ZFILE_ICON_EMPTY        ICON                    "../res/none.ico"

ZFILE_ICON_MINUS        ICON                    "../res/folder-minus.ico"

ZFILE_ICON_PLUS         ICON                    "../res/folder-plus.ico"

FINDER_YESBOXED         ICON                    "../res/sandbox-full.ico"

FINDER_NOTBOXED         ICON                    "../res/bigex.ico"


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

WAIT_MENU MENU
BEGIN
    POPUP "x"
    BEGIN
        MENUITEM "3487",                        ID_SHOW_ERRORS
        MENUITEM "3417",                        ID_EXIT
    END
END

TOP_MENU MENU
BEGIN
    POPUP "3411"
    BEGIN
        MENUITEM "3412",                        ID_TERMINATE_ALL
        MENUITEM "3413",                        ID_DISABLE_FORCE
        MENUITEM SEPARATOR
        MENUITEM "3415",                        ID_FINDER_OPEN
        MENUITEM "3416",                        ID_RESOURCE_MONITOR
        MENUITEM SEPARATOR
        MENUITEM "3417",                        ID_EXIT
    END
    POPUP "3421"
    BEGIN
        MENUITEM "3426",                        ID_VIEW_TOPMOST
        MENUITEM SEPARATOR
        MENUITEM "3422",                        ID_VIEW_PROCESS
        MENUITEM "3423",                        ID_VIEW_FILES
        MENUITEM SEPARATOR
        MENUITEM "3424",                        ID_VIEW_CONTEXT
        MENUITEM SEPARATOR
        MENUITEM "3425",                        ID_VIEW_RECOVERY_LOG
    END
    POPUP "3431"
    BEGIN
        MENUITEM SEPARATOR
        MENUITEM "3432",                        ID_SANDBOX_CREATE_NEW
        MENUITEM "3433",                        ID_SANDBOX_SET_FOLDER
        MENUITEM "3435",                        ID_SANDBOX_SET_LAYOUT
    END
    POPUP "3441"
    BEGIN
        MENUITEM "3442",                        ID_CONF_ALERT
        MENUITEM "3443",                        ID_CONF_SHELL
        MENUITEM "3501",                        ID_CONF_THIRD_PARTY
        MENUITEM "3444",                        ID_CONF_HIDDEN_MSG
        POPUP "3445"
        BEGIN
            MENUITEM "3448",                        ID_CONF_SHOW_ALL_TIPS
            MENUITEM "3449",                        ID_CONF_HIDE_ALL_TIPS
        END
        MENUITEM SEPARATOR
        MENUITEM "3502",                        ID_CONF_LOCK
        MENUITEM "3446",                        ID_CONF_EDIT
        MENUITEM "3447",                        ID_CONF_RELOAD
    END
    POPUP "3451"
    BEGIN
        MENUITEM "3504",                        ID_HELP_SUPPORT
        MENUITEM "3505",                        ID_HELP_CONTRIBUTION
        MENUITEM SEPARATOR
        MENUITEM "3452",                        ID_HELP_TOPICS
        MENUITEM "3453",                        ID_HELP_TUTORIAL
        MENUITEM "3469",                        ID_HELP_WHATSNEW
        //MENUITEM "3468",                        ID_HELP_MIGRATION
        MENUITEM "3457",                        ID_HELP_FORUM
        MENUITEM SEPARATOR
        MENUITEM "3454",                        ID_HELP_UPDATE
        MENUITEM "3467",                        ID_HELP_UPGRADE
        MENUITEM SEPARATOR
        MENUITEM "3506",                        ID_HELP_GET_CERT
        MENUITEM "3507",                        ID_HELP_SET_CERT
        MENUITEM SEPARATOR
        MENUITEM "3456",                        ID_HELP_ABOUT
    END
    MENUITEM "3467",                        ID_HELP_UPGRADE
END

SANDBOX_MENU MENU
BEGIN
    POPUP "3461"
    BEGIN
        MENUITEM "3462",                        ID_SANDBOX_RUN_BROWSER
        MENUITEM "3463",                        ID_SANDBOX_RUN_MAILER
        MENUITEM "3464",                        ID_SANDBOX_RUN_ANY
        MENUITEM "3465",                        ID_SANDBOX_RUN_MENU
        MENUITEM "3466",                        ID_SANDBOX_RUN_EXPLORER
    END
    MENUITEM SEPARATOR
    MENUITEM "3471",                        ID_SANDBOX_TERMINATE
    MENUITEM "3472",                        ID_SANDBOX_RECOVER
    MENUITEM "3473",                        ID_SANDBOX_DELETE
    MENUITEM "3474",                        ID_SANDBOX_EXPLORE
    MENUITEM SEPARATOR
    MENUITEM "3475",                        ID_SANDBOX_SETTINGS
    MENUITEM SEPARATOR
    MENUITEM "3476",                        ID_SANDBOX_RENAME
    MENUITEM "3477",                        ID_SANDBOX_REMOVE
END

PROCESS_MENU MENU
BEGIN
    POPUP ""
    BEGIN
        MENUITEM "3481",                        ID_PROCESS_TERMINATE
        MENUITEM SEPARATOR
        MENUITEM "3482",                        ID_PROCESS_SETTINGS
        MENUITEM "3483",                        ID_PROCESS_RESOURCES
    END
END

ZFILE_MENU MENU
BEGIN
    POPUP ""
    BEGIN
        MENUITEM "3461",                        ID_FILE_RUN
        MENUITEM SEPARATOR
        MENUITEM "3491",                        ID_FILE_RECOVER_SAME
        MENUITEM "3492",                        ID_FILE_RECOVER_ANY
        MENUITEM "3005",                        ID_FILE_RECOVER_CUT
        MENUITEM SEPARATOR
        MENUITEM "3493",                        ID_FILE_RECOVER_ADD
        MENUITEM "3494",                        ID_FILE_RECOVER_REMOVE
        MENUITEM SEPARATOR
        MENUITEM "3495",                        ID_FILE_CREATE_SHORTCUT
    END
END

TRAY_MENU MENU
BEGIN
    POPUP ""
    BEGIN
        MENUITEM "",                            ID_SHOW_WINDOW
        MENUITEM SEPARATOR
        MENUITEM SEPARATOR
        MENUITEM "3412",                        ID_TERMINATE_ALL
        MENUITEM "3413",                        ID_DISABLE_FORCE
        MENUITEM SEPARATOR
        MENUITEM "3417",                        ID_EXIT
    END
END

TRAY_SANDBOX_MENU MENU
BEGIN
    MENUITEM "3462",                        ID_SANDBOX_RUN_BROWSER
    MENUITEM "3463",                        ID_SANDBOX_RUN_MAILER
    MENUITEM "3464",                        ID_SANDBOX_RUN_ANY
    MENUITEM "3465",                        ID_SANDBOX_RUN_MENU
    MENUITEM "3466",                        ID_SANDBOX_RUN_EXPLORER
    MENUITEM SEPARATOR
    MENUITEM "3471",                        ID_SANDBOX_TERMINATE
    MENUITEM "3472",                        ID_SANDBOX_RECOVER
    MENUITEM "3473",                        ID_SANDBOX_DELETE
    MENUITEM "3474",                        ID_SANDBOX_EXPLORE
END


/////////////////////////////////////////////////////////////////////////////
//
// IMAGE
//

BACKGROUND              IMAGE                   "../res/background.png"

MASTHEADLOGO            IMAGE                   "../res/MastheadLogo.jpg"

WELCOME_ANIM            IMAGE                   "../res/FrontPageAnimation.gif"

WELCOME_LEGEND          IMAGE                   "../res/GettingStartedLegend.png"

BLUE_EXCLAMATION        IMAGE                   "../res/exclamation.png"

BLUE_CHECKMARK          IMAGE                   "../res/checkmark.png"

BLUE_QUESTIONMARK       IMAGE                   "../res/questionmark.png"

BLUE_CYCLE              IMAGE                   "../res/cycle.png"


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

ABOUT_DIALOG DIALOGEX 0, 0, 287, 211
STYLE DS_SETFONT | DS_MODALFRAME | DS_3DLOOK | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    GROUPBOX        "",ID_ABOUT_FRAME,27,8,226,60
    CONTROL         "",ID_ABOUT_LOGO,"Static",SS_BITMAP | WS_GROUP,35,15,5,5
    CTEXT           "",ID_ABOUT_VERSION,15,80,255,10
    CTEXT           "",ID_ABOUT_COPYRIGHT,15,95,255,20
    CTEXT           "",ID_ABOUT_INFO,15,130,255,20
    DEFPUSHBUTTON   "",IDOK,115,180,55,14
END

MESSAGE_DIALOG DIALOGEX 0, 0, 412, 173
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    LISTBOX         ID_MESSAGE_LIST,10,10,392,110,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_MESSAGE_HELP,104,130,50,14
    PUSHBUTTON      "",ID_MESSAGE_HIDE,180,130,50,14,NOT WS_VISIBLE
    DEFPUSHBUTTON   "",IDOK,256,130,50,14
    PUSHBUTTON      "",ID_MESSAGE_COPY,104,150,202,14
END

DISABLE_FORCE_DIALOG DIALOG 0, 0, 302, 146
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    EDITTEXT        ID_DISABLE_FORCE_EDIT,177,10,40,15,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "",ID_DISABLE_FORCE_SPIN,"msctls_updown32",UDS_SETBUDDYINT | UDS_AUTOBUDDY | UDS_ARROWKEYS | WS_TABSTOP,217,10,12,14
    LTEXT           "",ID_DISABLE_FORCE_LABEL_1,5,10,165,15,SS_CENTERIMAGE
    LTEXT           "",ID_DISABLE_FORCE_LABEL_2,237,10,45,15,SS_CENTERIMAGE
    LTEXT           "",ID_DISABLE_FORCE_LABEL_3,5,40,290,35
    LTEXT           "",ID_DISABLE_FORCE_LABEL_4,5,80,290,35
    DEFPUSHBUTTON   "",IDOK,70,125,50,14
    PUSHBUTTON      "",IDCANCEL,190,125,50,14
END

MONITOR_DIALOG DIALOG 0, 0, 500, 200
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_MESSAGE_HELP,10,7,480,25
    LISTBOX         ID_MESSAGE_LIST,10,30,480,140,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    DEFPUSHBUTTON   "",IDOK,160,180,200,14
END

FINDER_TOOL DIALOG 0, 0, 177, 86
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    CONTROL         "",ID_FINDER_TARGET,"Static",SS_BITMAP | SS_NOTIFY,10,10,30,20
    LTEXT           "",ID_FINDER_EXPLAIN,50,10,120,65
    ICON            "FINDER_YESBOXED",ID_FINDER_YES_BOXED,10,80,32,32,NOT WS_VISIBLE
    ICON            "FINDER_NOTBOXED",ID_FINDER_NOT_BOXED,10,80,32,32,NOT WS_VISIBLE
    LTEXT           "",ID_FINDER_RESULT,50,80,120,65,NOT WS_VISIBLE
    PUSHBUTTON      "",IDCANCEL,60,150,60,20,NOT WS_VISIBLE
END

CREATE_DIALOG DIALOG 0, 0, 339, 160
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_CREATE_EXPLAIN,7,7,325,15
    EDITTEXT        ID_CREATE_NAME,58,25,233,14,ES_AUTOHSCROLL
    LTEXT           "",ID_CREATE_COPY_TEXT,7,50,325,15
    COMBOBOX        ID_CREATE_COPY_COMBO,58,70,233,80,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    DEFPUSHBUTTON   "",IDOK,95,100,50,14
    PUSHBUTTON      "",IDCANCEL,193,100,50,14
    LTEXT           "",ID_CREATE_ERROR,7,130,325,25,NOT WS_VISIBLE
END

SETFOLDER_DIALOG DIALOG 0, 0, 337, 166
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_SETFOLDER_EXPLAIN_1,10,10,225,20
    LISTBOX         ID_SETFOLDER_DRIVES,245,10,80,95,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "",ID_SETFOLDER_EXPLAIN_2,10,30,225,25
    LTEXT           "",ID_SETFOLDER_EXPLAIN_3,10,60,225,20
    EDITTEXT        ID_SETFOLDER_PATH,10,90,220,14,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "",IDOK,110,115,50,14
    PUSHBUTTON      "",IDCANCEL,175,115,50,14
    LTEXT           "",ID_SETFOLDER_WARN,10,140,315,20,NOT WS_VISIBLE
END

REVEAL_DIALOG DIALOG 0, 0, 339, 155
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_REVEAL_EXPLAIN_1,7,7,325,15
    COMBOBOX        ID_REVEAL_COMBO,58,25,233,14,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "",ID_REVEAL_EXPLAIN_2,7,50,325,15
    EDITTEXT        ID_REVEAL_EDIT,58,70,233,30,ES_MULTILINE | ES_AUTOVSCROLL | ES_READONLY
    LTEXT           "",ID_REVEAL_EXPLAIN_3,7,110,325,23
    DEFPUSHBUTTON   "",IDOK,95,135,50,14
    PUSHBUTTON      "",IDCANCEL,193,135,50,14
END

ALERT_DIALOG DIALOG 0, 0, 302, 171
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_ALERT_EXPLAIN,10,10,280,20
    LISTBOX         ID_ALERT_LIST,10,40,200,95,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_ALERT_ADD,220,45,70,20,BS_MULTILINE
    PUSHBUTTON      "",ID_ALERT_REMOVE,220,110,70,20,BS_MULTILINE
    DEFPUSHBUTTON   "",IDOK,95,145,50,14
    PUSHBUTTON      "",IDCANCEL,160,145,50,14
END

SHELL_DIALOG DIALOG 0, 0, 372, 250
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    GROUPBOX        "",ID_SHELL_GROUP_1,10,10,350,50
    CONTROL         "",ID_SHELL_RUNLOGON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,25,330,10
    CONTROL         "",ID_SHELL_RUNBOXED,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,40,330,10
    GROUPBOX        "",ID_SHELL_GROUP_2,10,75,350,70
    CONTROL         "",ID_SHELL_DESKTOP,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,90,330,10
    CONTROL         "",ID_SHELL_QUICKLAUNCH,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,105,330,10
    LTEXT           "",ID_SHELL_LABEL_1,20,120,210,15,SS_CENTERIMAGE
    PUSHBUTTON      "",ID_SHELL_SHORTCUTS,235,120,115,15
    GROUPBOX        "",ID_SHELL_GROUP_3,10,160,350,50
    CONTROL         "",ID_SHELL_CONTEXT,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,175,330,10
    CONTROL         "",ID_SHELL_SENDTO,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,20,190,330,10
    DEFPUSHBUTTON   "",IDOK,125,225,50,14
    PUSHBUTTON      "",IDCANCEL,225,225,50,14
END

DELETE_DIALOG DIALOG 0, 0, 418, 275
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_DELETE_EXPLAIN_1,5,5,405,25
    LTEXT           "",ID_RECOVER_TREE,65,35,345,155,NOT WS_VISIBLE
    PUSHBUTTON      "",ID_RECOVER_SELECT_ALL,380,35,10,10
    DEFPUSHBUTTON   "",ID_RECOVER_SAME,5,35,55,30,BS_MULTILINE
    PUSHBUTTON      "",ID_RECOVER_ANY,5,75,55,30,BS_MULTILINE
    PUSHBUTTON      "",ID_RECOVER_ADD,5,115,55,30,BS_MULTILINE
    GROUPBOX        "",ID_RECOVER_GROUPBOX,5,200,405,65
    CTEXT           "",ID_DELETE_SIZE,10,210,390,15
    CTEXT           "",ID_DELETE_EXPLAIN_2,10,225,390,10
    PUSHBUTTON      "",IDOK,15,240,190,19
    PUSHBUTTON      "",IDCANCEL,225,240,175,19
    PUSHBUTTON      "",IDCLOSE,5,170,55,20
END

AUTORECOVER_DIALOG DIALOG 0, 0, 339, 245
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_DELETE_EXPLAIN_1,10,7,315,13
    LISTBOX         ID_RECOVER_ITEMS,10,22,320,85,LBS_WANTKEYBOARDINPUT | LBS_EXTENDEDSEL | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_RECOVER_SELECT_ALL,1,1,10,10,NOT WS_VISIBLE
    LTEXT           "",ID_DELETE_EXPLAIN_2,10,104,315,13
    LISTBOX         ID_RECOVER_FOLDERS,10,120,320,85,WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_RECOVER_RIGHT_CLICK,1,1,10,10,NOT WS_VISIBLE
    DEFPUSHBUTTON   "",IDOK,10,202,125,18
    PUSHBUTTON      "",ID_RECOVER_CYCLE,140,202,10,10,BS_BITMAP | BS_CENTER | NOT WS_VISIBLE
    PUSHBUTTON      "",IDCANCEL,205,202,125,18
    CHECKBOX        "",ID_RECOVER_DISABLE,10,228,310,10
    PUSHBUTTON      "",ID_RECOVER_REMOVE,1,1,10,10,NOT WS_VISIBLE
END

PROC_SETTINGS_DIALOG DIALOG 0, 0, 417, 210
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    GROUPBOX        "",IDC_STATIC,5,2,405,25
    LTEXT           "",ID_SETTINGS_SANDBOX_LABEL,10,8,55,15,SS_CENTERIMAGE
    LTEXT           "",ID_SETTINGS_PROGRAM_LABEL,225,8,55,15,SS_CENTERIMAGE
    LTEXT           "",ID_SETTINGS_SANDBOX,65,8,155,15,SS_CENTERIMAGE
    LTEXT           "",ID_SETTINGS_PROGRAM,285,8,120,15,SS_CENTERIMAGE
    CONTROL         "",ID_SETTINGS_PAGE_1,"Button",BS_AUTORADIOBUTTON | WS_GROUP,100,32,80,15
    CONTROL         "",ID_SETTINGS_PAGE_2,"Button",BS_AUTORADIOBUTTON,250,32,80,15
    DEFPUSHBUTTON   "",IDOK,111,190,50,14
    PUSHBUTTON      "",IDCANCEL,251,190,50,14
    GROUPBOX        "",ID_SETTINGS_GROUP1P1,5,50,405,71,NOT WS_VISIBLE
    LTEXT           "",ID_SETTINGS_LABEL_P1,15,63,390,15,NOT WS_VISIBLE
    CONTROL         "",ID_SETTINGS_ALERT,"Button",BS_AUTOCHECKBOX | NOT WS_VISIBLE | WS_TABSTOP,15,75,390,15
    CONTROL         "",ID_SETTINGS_FORCE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,15,90,390,15
    LTEXT           "",ID_SETTINGS_REGISTER,35,105,370,15,NOT WS_VISIBLE
    GROUPBOX        "",ID_SETTINGS_GROUP2P1,5,130,405,48,NOT WS_VISIBLE
    CONTROL         "",ID_SETTINGS_LINGER,"Button",BS_AUTOCHECKBOX | NOT WS_VISIBLE | WS_TABSTOP,15,142,390,15
    CONTROL         "",ID_SETTINGS_LEADER,"Button",BS_AUTOCHECKBOX | NOT WS_VISIBLE | WS_TABSTOP,15,157,390,15
    GROUPBOX        "",ID_SETTINGS_GROUP1P2,5,50,405,60,NOT WS_VISIBLE
    LTEXT           "",ID_SETTINGS_INTERNET_LIST,15,63,390,45,NOT WS_VISIBLE
    CONTROL         "",ID_SETTINGS_INTERNET,"Button",BS_AUTOCHECKBOX | NOT WS_VISIBLE | WS_TABSTOP,15,90,390,15
    GROUPBOX        "",ID_SETTINGS_GROUP2P2,5,118,405,60,NOT WS_VISIBLE
    LTEXT           "",ID_SETTINGS_STARTRUN_LIST,15,131,390,15,NOT WS_VISIBLE
    CONTROL         "",ID_SETTINGS_STARTRUN,"Button",BS_AUTOCHECKBOX | NOT WS_VISIBLE | WS_TABSTOP,15,158,390,15
END

BOXTITLE_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 10, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,30
    CONTROL         "",ID_HIDE_INDICATOR,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,56,340,10
    LTEXT           "",ID_PAGE_LABEL_2,15,80,345,30
    CONTROL         "",ID_BOXNAMETITLE,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,110,340,10
    CONTROL         "",IDC_STATIC,"Static",SS_GRAYRECT | WS_GROUP,5,130,350,1
    LTEXT           "",ID_PAGE_LABEL_3,15,140,345,30
    CONTROL         "",ID_SHOW_BORDER,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,16,170,300,12
    PUSHBUTTON      "",ID_BORDER_COLOR,163,186,20,22,BS_BITMAP | BS_CENTER | WS_BORDER
    CONTROL         "",ID_BORDER_TITLE,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,212,355,26
    EDITTEXT        ID_BORDER_WIDTH,186,191,20,12,ES_AUTOHSCROLL
END

QUICKRECOVER_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,46
    LISTBOX         ID_RECOVER_FOLDERS,15,82,274,140,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_RECOVER_ADD,299,89,65,25,BS_MULTILINE
    PUSHBUTTON      "",ID_RECOVER_REMOVE,299,189,65,25,BS_MULTILINE
END

AUTORECOVER_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,30
    CONTROL         "",ID_RECOVER_AUTO,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,52,340,10
    LTEXT           "",ID_PAGE_LABEL_2,15,70,345,25
    LISTBOX         ID_RECOVER_FOLDERS,15,100,274,130,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_RECOVER_ADD_FOLDER,299,105,65,25,BS_MULTILINE
    PUSHBUTTON      "",ID_RECOVER_ADD_TYPE,299,150,65,25,BS_MULTILINE
    PUSHBUTTON      "",ID_RECOVER_REMOVE,299,200,65,25,BS_MULTILINE
END

AUTODELETE_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,40
    CONTROL         "",ID_DELETE_AUTO,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,60,340,10
    LTEXT           "",ID_PAGE_LABEL_2,15,90,345,40
    LTEXT           "",ID_PAGE_LABEL_3,15,130,345,40
    CONTROL         "",ID_DELETE_NEVER,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,170,340,10
END

DELETECOMMAND_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,40
    EDITTEXT        ID_DELETE_COMMAND,15,60,340,15,ES_AUTOHSCROLL
    LTEXT           "",ID_PAGE_LABEL_2,15,90,345,40
    LTEXT           "",ID_PAGE_LABEL_3,15,135,345,25
    PUSHBUTTON      "RMDIR",ID_DELETE_RMDIR,15,160,80,25
    PUSHBUTTON      "SDelete",ID_DELETE_SDELETE,110,160,80,25
    PUSHBUTTON      "Eraser-5",ID_DELETE_ERASERL,205,160,80,25
    PUSHBUTTON      "Eraser-6",ID_DELETE_ERASER6,205,200,80,25
END

PGMGROUP_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,50
    LTEXT           "",ID_GROUP_COMBO_TEXT,15,71,100,10
    COMBOBOX        ID_GROUP_COMBO,120,70,169,80,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_GROUP_ADD,295,65,75,25
    LISTBOX         ID_GROUP_LIST,15,95,274,140,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_GROUP_ADD_PROGRAM,295,105,75,25
    PUSHBUTTON      "",ID_GROUP_REMOVE,295,200,75,25
END

FORCEFOL_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,35
    LTEXT           "",ID_PAGE_LABEL_2,15,60,345,30
    LISTBOX         ID_PROGRAM_LIST,15,95,230,130,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_PROGRAM_ADD_FOLDER,255,100,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_PROGRAM_REMOVE,255,195,100,25,BS_MULTILINE
    CTEXT           "",ID_PROGRAM_REGISTER,5,225,340,10
END

FORCEPGM_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,39
    LISTBOX         ID_PROGRAM_LIST,15,65,230,160,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_PROGRAM_ADD,255,70,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_PROGRAM_REMOVE,255,195,100,25,BS_MULTILINE
    CTEXT           "",ID_PROGRAM_REGISTER,5,230,340,10
END

LINGERLEADER_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,38
    LISTBOX         ID_PROGRAM_LIST,15,62,230,160,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_PROGRAM_ADD,255,67,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_PROGRAM_REMOVE,255,192,100,25,BS_MULTILINE
END

FILEMIGRATE_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,49
    LTEXT           "",ID_PAGE_LABEL_2,15,75,345,46
    RTEXT           "",ID_PAGE_LABEL_3,15,130,170,12
    EDITTEXT        ID_MIGRATE_KB,193,129,45,14,ES_AUTOHSCROLL
    LTEXT           "",ID_PAGE_LABEL_4,245,130,45,12
    LTEXT           "",ID_MIGRATE_MB,295,130,40,12
    LTEXT           "",ID_PAGE_LABEL_5,15,159,345,27
    CONTROL         "",ID_MIGRATE_SILENT,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,30,189,320,15
END

RESTRICTPGM_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,30
    LISTBOX         ID_PROGRAM_LIST,15,52,230,135,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_PROGRAM_ADD,255,57,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_PROGRAM_REMOVE,255,107,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_PROGRAM_ADD_STAR,255,157,100,25,BS_MULTILINE
    CONTROL         "",ID_PROGRAM_NOTIFY,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,192,340,15
    LTEXT           "",ID_PAGE_LABEL_2,15,215,315,30
END

DROPRIGHTS_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,25
    LTEXT           "",ID_PAGE_LABEL_2,15,60,345,39
    CONTROL         "",ID_DROPRIGHTS,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,105,340,10
    LTEXT           "",ID_PAGE_LABEL_3,15,130,345,58
    CONTROL         "",ID_PAGE_LABEL_4,"SysLink",WS_TABSTOP,15,196,345,35
END

NETWORK_FILES_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,25
    LTEXT           "",ID_PAGE_LABEL_2,15,55,345,53
    CONTROL         "",ID_BLOCKNETWORKFILES,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,115,340,10
    LTEXT           "",ID_PAGE_LABEL_3,15,135,345,32,NOT WS_VISIBLE
    LTEXT           "",ID_PAGE_LABEL_4,15,175,345,32,NOT WS_VISIBLE
END

/* PRINTSPOOLER_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,37
    LTEXT           "",ID_PAGE_LABEL_2,15,71,345,32
    CONTROL         "",ID_ALLOWSPOOLERPRINTTOFILE,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,123,340,10
    LTEXT           "",ID_PAGE_LABEL_3,15,130,345,32,NOT WS_VISIBLE
    LTEXT           "",ID_PAGE_LABEL_4,15,170,345,32,NOT WS_VISIBLE
END */

RESOURCE_ACCESS_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,344,25
    LTEXT           "",ID_PAGE_LABEL_2,15,45,344,20
    LTEXT           "",ID_FILE_PROGRAM_TEXT,15,71,99,10
    COMBOBOX        ID_FILE_PROGRAM,120,70,145,80,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_FILE_NEGATE,272,68,16,17,BS_BITMAP | BS_CENTER
    LTEXT           "",ID_FILE_PROGRAM_EXPLAIN,120,85,139,10,SS_ENDELLIPSIS
    PUSHBUTTON      "",ID_FILE_ADD_PROGRAM,295,65,74,25
    LISTBOX         ID_FILE_LIST,15,95,273,140,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_FILE_ADD,295,105,74,25
    PUSHBUTTON      "",ID_FILE_EDIT,295,152,74,25
    PUSHBUTTON      "",ID_FILE_REMOVE,295,200,74,25
END

NOT_IMPLEMENTED_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,344,142
    CONTROL         "",ID_PAGE_LABEL_2,"SysLink",WS_TABSTOP,15,169,344,73
END

USERACCOUNTS_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,45
    LTEXT           "",ID_PAGE_LABEL_2,15,60,345,36
    LISTBOX         ID_USER_LIST,15,100,230,140,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_USER_ADD,255,110,100,25,BS_MULTILINE
    PUSHBUTTON      "",ID_USER_REMOVE,255,205,100,25,BS_MULTILINE
END

APP_ACCESS_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,50
    LTEXT           "",ID_PAGE_LABEL_2,15,78,345,30
    CONTROL         "",ID_APP_SCREEN_READER,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,15,118,300,10
END

APP_TEMPLATE_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,36
    LISTBOX         ID_APP_TEMPLATE_LIST,15,60,345,137,LBS_OWNERDRAWFIXED | LBS_HASSTRINGS | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    CTEXT           "",ID_PAGE_LABEL_2,15,203,345,10
    PUSHBUTTON      "",ID_APP_TEMPLATE_ADD,15,220,70,15
    PUSHBUTTON      "",ID_APP_TEMPLATE_REMOVE,95,220,70,15
    PUSHBUTTON      "",ID_APP_TEMPLATE_VIEW,175,220,80,15
    PUSHBUTTON      "",ID_APP_TEMPLATE_LINK,265,220,95,15,NOT WS_VISIBLE
    PUSHBUTTON      "",ID_APP_TEMPLATE_CREATE,265,220,95,15,NOT WS_VISIBLE
END

APP_FOLDERS_PAGE DIALOG 0, 0, 382, 250
STYLE DS_SETFONT | WS_CHILD | WS_DISABLED | WS_CAPTION
FONT 8, "1"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,15,20,345,25
    LTEXT           "",ID_APP_FOLDERS_LABEL_1,15,50,105,10
    COMBOBOX        ID_APP_FOLDERS_COMBO,130,50,165,80,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "",ID_APP_FOLDERS_LABEL_2,15,80,200,10
    EDITTEXT        ID_APP_FOLDERS_DEFAULT,15,95,345,15,ES_AUTOHSCROLL | ES_READONLY
    LTEXT           "",ID_APP_FOLDERS_LABEL_3,15,136,200,10
    EDITTEXT        ID_APP_FOLDERS_OVERRIDE,15,155,345,15,ES_AUTOHSCROLL | ES_READONLY
    PUSHBUTTON      "",ID_APP_FOLDERS_ADD,130,135,80,15,BS_CENTER | NOT WS_VISIBLE
    PUSHBUTTON      "",ID_APP_FOLDERS_REMOVE,220,135,80,15,BS_CENTER | NOT WS_VISIBLE
END

VIEW_TEMPLATE_DIALOG DIALOG 0, 0, 450, 200
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,10,10,430,25
    EDITTEXT        IDTEXT,10,35,430,135,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_WANTRETURN
    DEFPUSHBUTTON   "",IDOK,120,177,70,14
    PUSHBUTTON      "",IDCANCEL,260,177,70,14
END

THIRD_PARTY_DIALOG DIALOG 0, 0, 390, 190
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,10,10,350,25
    LTEXT           "",ID_PAGE_LABEL_2,10,25,350,25
    LTEXT           "",ID_PAGE_LABEL_3,10,40,350,25
    LISTBOX         ID_APP_TEMPLATE_LIST,10,60,300,80,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "",ID_APP_TEMPLATE_ADD,320,80,60,15
    PUSHBUTTON      "",ID_APP_TEMPLATE_REMOVE,320,100,60,15
    DEFPUSHBUTTON   "",IDOK,80,150,70,15
    PUSHBUTTON      "",IDCANCEL,160,150,70,15
    PUSHBUTTON      "",ID_APP_TEMPLATE_REMOVE_OLD,280,150,100,15,NOT WS_VISIBLE
    PUSHBUTTON      "",ID_APP_TEMPLATE_CONFLICTS,280,170,100,15,NOT WS_VISIBLE
    CHECKBOX        "",ID_APP_TEMPLATE_AUTORUN,10,170,260,15
END

LOCK_CONFIG_DIALOG DIALOG 0, 0, 400, 150
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_PAGE_LABEL_1,10,10,380,25
    CONTROL         "",ID_LOCK_ADMIN_ONLY,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,40,390,15
    CHECKBOX        "",ID_LOCK_ENABLE_PASSWORD,10,55,390,15
    PUSHBUTTON      "",ID_LOCK_CHANGE_PASSWORD,100,70,100,15,NOT WS_VISIBLE
    CONTROL         "",ID_LOCK_FORGET_PASSWORD,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,90,400,15
    CONTROL         "",ID_LOCK_FORCE_ADMIN_ONLY,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,105,400,15
    DEFPUSHBUTTON   "",IDOK,100,130,70,15
    PUSHBUTTON      "",IDCANCEL,200,130,70,15
END

PROGRAM_SELECTOR DIALOG 0, 0, 500, 240
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_PGMSEL_TEXT1,10,10,160,25
    LISTBOX         ID_PGMSEL_LIST1,10,30,160,130,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    LTEXT           "",ID_PGMSEL_TEXT2,180,10,160,25
    LISTBOX         ID_PGMSEL_LIST2,180,30,160,130,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    LTEXT           "",ID_PGMSEL_TEXT3,350,10,140,25
    LISTBOX         ID_PGMSEL_LIST3,350,30,140,130,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | WS_TABSTOP
    CTEXT           "",ID_PGMSEL_TEXT4,10,183,160,25
    EDITTEXT        ID_PGMSEL_INPUT,180,180,160,15,ES_AUTOVSCROLL | ES_AUTOHSCROLL
    PUSHBUTTON      "",ID_PGMSEL_OPEN,370,175,100,25
    DEFPUSHBUTTON   "",IDOK,135,210,80,20
    PUSHBUTTON      "",IDCANCEL,305,210,80,20
END

SETLAYOUT_DIALOG DIALOG 0, 0, 300, 240
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_SETLAYOUT_EXPLAIN_1,10,10,280,35
    LTEXT           "",ID_SETLAYOUT_TREE_PLACEHOLDER,50,50,200,150
    DEFPUSHBUTTON   "",IDOK,150,215,60,15
    PUSHBUTTON      "",IDCANCEL,230,215,60,15
END

RUN_BROWSER_DIALOG DIALOG 0, 0, 372, 86
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    LTEXT           "",ID_RUN_BROWSER_EXPLAIN,10,10,350,15
    EDITTEXT        ID_RUN_BROWSER_URL,10,30,350,15,ES_CENTER | ES_AUTOHSCROLL | ES_READONLY
    DEFPUSHBUTTON   "",IDYES,57,65,75,14
    PUSHBUTTON      "",IDCANCEL,239,65,75,14
    PUSHBUTTON      "",IDNO,148,65,75,14
END

UPDATE_DIALOG DIALOG 0, 0, 367, 111
STYLE DS_SETFONT | DS_MODALFRAME | DS_3DLOOK | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg"
BEGIN
    CTEXT           "",ID_UPDATE_EXPLAIN_1,5,10,345,15,SS_CENTERIMAGE
    DEFPUSHBUTTON   "",1,20,40,70,14
    PUSHBUTTON      "",ID_UPDATE_LATER,106,40,70,14
    PUSHBUTTON      "",ID_UPDATE_NEVER,192,40,70,14
    PUSHBUTTON      "",2,278,40,70,14
    CONTROL         "",ID_UPDATE_SILENT,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,92,65,182,15
    CTEXT           "",ID_UPDATE_EXPLAIN_2,10,90,345,15
END


/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

FINDER_FULL             BITMAP                  "../res/finder-full.bmp"

FINDER_EMPTY            BITMAP                  "../res/finder-empty.bmp"


/////////////////////////////////////////////////////////////////////////////
//
// Cursor
//

FINDER_CURSOR           CURSOR                  "../res/finder.cur"


/////////////////////////////////////////////////////////////////////////////
//
// RT_MANIFEST
//

1                       RT_MANIFEST             "../res/xptheme.manifest"


#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "#include ""verrsrc.h""\r\n"
    "#include ""common/my_version.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#include ""SbieControl.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    "ABOUT_DIALOG", DIALOG
    BEGIN
    END

    "MESSAGE_DIALOG", DIALOG
    BEGIN
        LEFTMARGIN, 10
        RIGHTMARGIN, 402
    END

    "REVEAL_DIALOG", DIALOG
    BEGIN
    END

    "DELETE_DIALOG", DIALOG
    BEGIN
    END

    "BOXTITLE_PAGE", DIALOG
    BEGIN
        VERTGUIDE, 316
    END

    "AUTORECOVER_PAGE", DIALOG
    BEGIN
    END

    "AUTODELETE_PAGE", DIALOG
    BEGIN
    END

    "FILEMIGRATE_PAGE", DIALOG
    BEGIN
    END

    "NOT_IMPLEMENTED_PAGE", DIALOG
    BEGIN
    END

    "USERACCOUNTS_PAGE", DIALOG
    BEGIN
    END

    "VIEW_TEMPLATE_DIALOG", DIALOG
    BEGIN
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

ABOUT_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

BOXTITLE_PAGE AFX_DIALOG_LAYOUT
BEGIN
    0
END

NOT_IMPLEMENTED_PAGE AFX_DIALOG_LAYOUT
BEGIN
    0
END

#endif    // English (United States) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#include "SbieControl.rc2"  // non-Microsoft Visual C++ edited resources

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

